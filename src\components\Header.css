/* Header Container */
.header {
  width: 100%;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 1000;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

/* Login Section */
.login {
  display: flex;
  align-items: center;
  flex: 1;
}

.login-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 12px 24px;
  background: linear-gradient(135deg, #312f2f 0%, #333132 100%);
  color: #ffffff;
  border: none;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.login-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(226, 54, 54, 0.3);
}

.login-btn a {
  text-decoration: none;
  color: inherit;
  font-weight: inherit;
}

.profile-icon {
  display: flex;
  align-items: center;
  color: #ffffff;
}

/* Logo Section */
.logo {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  z-index: 10;
}

.logo img {
  width: 130px;
  height: auto;
  max-height: 60px;
  object-fit: contain;
  filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.3));
  transition: transform 0.3s ease;
}

.logo img:hover {
  transform: scale(1.05);
}

/* Search Section */
.search {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-end;
}

.search img {
  width: 220px;
  height: auto;
  max-height: 45px;
  object-fit: contain;
  cursor: pointer;
  transition: all 0.3s ease;
  filter: brightness(0.9);
}

.search img:hover {
  filter: brightness(1.1);
  transform: scale(1.02);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .header-container {
    padding: 12px 20px;
  }

  .logo img {
    width: 140px;
    max-height: 50px;
  }

  .search img {
    width: 180px;
    max-height: 40px;
  }
}

@media (max-width: 768px) {
  .header-container {
    padding: 10px 16px;
    flex-wrap: wrap;
    gap: 12px;
  }

  .login {
    order: 1;
    flex: 0 0 auto;
  }

  .search {
    order: 2;
    flex: 0 0 auto;
    justify-content: flex-start;
  }

  .logo {
    order: 3;
    position: static;
    transform: none;
    width: 100%;
    justify-content: center;
    margin-top: 8px;
  }

  .logo img {
    width: 120px;
    max-height: 40px;
  }

  .search img {
    width: 160px;
    max-height: 35px;
  }

  .login-btn {
    padding: 10px 20px;
    font-size: 0.8rem;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .header-container {
    padding: 8px 12px;
  }

  .logo img {
    width: 100px;
    max-height: 35px;
  }

  .search img {
    width: 140px;
    max-height: 30px;
  }

  .login-btn {
    padding: 8px 16px;
    font-size: 0.75rem;
  }
}
