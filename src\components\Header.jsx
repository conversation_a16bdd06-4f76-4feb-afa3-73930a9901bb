import React from "react";
import "./Header.css";
import logo from "../assets/logo.jpeg";
import search from "../assets/search.jpeg";
import { CgProfile } from "react-icons/cg";

const Header = () => {
  return (
    <header className="header">
      <div className="header-container">
        <div className="login">
          <button className="login-btn" aria-label="Login or Sign up">
            <div className="profile-icon">
              <CgProfile size={20} />
            </div>
            <span>LOGIN | SIGNUP</span>
          </button>
        </div>

        <div className="logo">
          <img src={logo} alt="Marvel Logo" className="logo" />
        </div>

        <div className="search">
          <img src={search} alt="Search" />
        </div>
      </div>
    </header>
  );
};

export default Header;
